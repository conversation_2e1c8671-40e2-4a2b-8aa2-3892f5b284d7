import java.util.LinkedList;
class BrowserHistory {
    private LinkedList<String> history;
    private int currentPosition;

    // Constructor - sets up a new browser session
    public BrowserHistory() {
        this.history = new LinkedList<>();
        this.currentPosition = -1;
    }

    // Visit a new website - adds to history
    public void visitPage(String url) {
        // Remove any forward history when visiting a new page
        while (history.size() > currentPosition + 1) {
            history.removeLast();
        }

        history.add(url);
        currentPosition++;
        System.out.println("Visiting: " + url);
    }

    // Go back to previous page
    public String goBack() {
        if (canGoBack()) {
            currentPosition--;
            String page = history.get(currentPosition);
            System.out.println("Going back to: " + page);
            return page;
        } else {
            System.out.println("Can't go back - you're at the beginning!");
            return getCurrentPage();
        }
    }

    // Go forward to next page
    public String goForward() {
        if (canGoForward()) {
            currentPosition++;
            String page = history.get(currentPosition);
            System.out.println("Going forward to: " + page);
            return page;
        } else {
            System.out.println("Can't go forward - you're at the latest page!");
            return getCurrentPage();
        }
    }

    // Check if we can go back
    public boolean canGoBack() {
        return currentPosition > 0;
    }

    // Check if we can go forward
    public boolean canGoForward() {
        return currentPosition < history.size() - 1;
    }

    // Get current page
    public String getCurrentPage() {
        if (currentPosition >= 0 && currentPosition < history.size()) {
            return history.get(currentPosition);
        }
        return "No page loaded";
    }

    // Show complete browsing history
    public void showHistory() {
        System.out.println("\nYour Browsing History:");
        for (int i = 0; i < history.size(); i++) {
            String marker = (i == currentPosition) ? " (Current)" : "";
            System.out.println((i + 1) + ". " + history.get(i) + marker);
        }
        System.out.println();
    }
}

// Scenario 2: Music Playlist Queue
// This class manages a dynamic music playlist
class MusicPlaylist {
    private LinkedList<String> playlist;
    private int currentSongIndex;
    private boolean isPlaying;

    // Constructor - creates an empty playlist
    public MusicPlaylist() {
        this.playlist = new LinkedList<>();
        this.currentSongIndex = -1;
        this.isPlaying = false;
    }

    // Add a song to the end of playlist
    public void addSong(String songName) {
        playlist.add(songName);
        System.out.println("Added to playlist: " + songName);

        // If this is the first song, make it current
        if (playlist.size() == 1) {
            currentSongIndex = 0;
        }
    }

    // Add a song to the beginning (play next)
    public void addSongNext(String songName) {
        if (currentSongIndex >= 0) {
            playlist.add(currentSongIndex + 1, songName);
            System.out.println("Added to play next: " + songName);
        } else {
            addSong(songName);
        }
    }

    // Remove a song from playlist
    public boolean removeSong(String songName) {
        int index = playlist.indexOf(songName);
        if (index != -1) {
            playlist.remove(index);
            System.out.println("Removed from playlist: " + songName);

            // Adjust current index if needed
            if (index < currentSongIndex) {
                currentSongIndex--;
            } else if (index == currentSongIndex && currentSongIndex >= playlist.size()) {
                currentSongIndex = playlist.size() - 1;
            }
            return true;
        } else {
            System.out.println("Song not found in playlist: " + songName);
            return false;
        }
    }

    // Play current song
    public void play() {
        if (!playlist.isEmpty() && currentSongIndex >= 0) {
            isPlaying = true;
            System.out.println("Now playing: " + playlist.get(currentSongIndex));
        } else {
            System.out.println("No songs in playlist to play!");
        }
    }

    // Pause current song
    public void pause() {
        if (isPlaying) {
            isPlaying = false;
            System.out.println("Paused: " + getCurrentSong());
        } else {
            System.out.println("Nothing is currently playing!");
        }
    }

    // Play next song
    public String playNext() {
        if (currentSongIndex < playlist.size() - 1) {
            currentSongIndex++;
            String nextSong = playlist.get(currentSongIndex);
            isPlaying = true;
            System.out.println("⏭️ Playing next: " + nextSong);
            return nextSong;
        } else {
            System.out.println("🔚 End of playlist reached!");
            return getCurrentSong();
        }
    }

    // Play previous song
    public String playPrevious() {
        if (currentSongIndex > 0) {
            currentSongIndex--;
            String prevSong = playlist.get(currentSongIndex);
            isPlaying = true;
            System.out.println("⏮️ Playing previous: " + prevSong);
            return prevSong;
        } else {
            System.out.println("🔚 Already at the first song!");
            return getCurrentSong();
        }
    }

    // Get current song
    public String getCurrentSong() {
        if (currentSongIndex >= 0 && currentSongIndex < playlist.size()) {
            return playlist.get(currentSongIndex);
        }
        return "No song selected";
    }

    // Show entire playlist
    public void showPlaylist() {
        System.out.println("\n🎶 Your Playlist:");
        if (playlist.isEmpty()) {
            System.out.println("Empty playlist - add some songs!");
            return;
        }

        for (int i = 0; i < playlist.size(); i++) {
            String marker = "";
            if (i == currentSongIndex) {
                marker = isPlaying ? " 🎵 (Playing)" : " ⏸️ (Paused)";
            }
            System.out.println((i + 1) + ". " + playlist.get(i) + marker);
        }
        System.out.println();
    }

    // Get playlist size
    public int getPlaylistSize() {
        return playlist.size();
    }
}

// Main class to demonstrate both scenarios
public class brower {
    public static void main(String[] args) {
        System.out.println("🌐 Welcome to Browser & Music Demo!");
        System.out.println("=====================================\n");

        // Demonstrate Browser History
        System.out.println("📱 BROWSER HISTORY DEMO:");
        System.out.println("------------------------");

        BrowserHistory browser = new BrowserHistory();

        // Visit some websites
        browser.visitPage("google.com");
        browser.visitPage("youtube.com");
        browser.visitPage("github.com");
        browser.visitPage("stackoverflow.com");

        // Show history
        browser.showHistory();

        // Navigate back and forward
        browser.goBack();
        browser.goBack();
        browser.goForward();
        browser.visitPage("linkedin.com");

        browser.showHistory();

        System.out.println("\n" + "=".repeat(50) + "\n");

        // Demonstrate Music Playlist
        System.out.println("🎵 MUSIC PLAYLIST DEMO:");
        System.out.println("-----------------------");

        MusicPlaylist playlist = new MusicPlaylist();

        // Add some songs
        playlist.addSong("Shape of You - Ed Sheeran");
        playlist.addSong("Blinding Lights - The Weeknd");
        playlist.addSong("Watermelon Sugar - Harry Styles");
        playlist.addSongNext("Good 4 U - Olivia Rodrigo");

        // Show playlist and play
        playlist.showPlaylist();
        playlist.play();

        // Navigate through songs
        playlist.playNext();
        playlist.playNext();
        playlist.playPrevious();

        // Remove a song and show updated playlist
        playlist.removeSong("Blinding Lights - The Weeknd");
        playlist.showPlaylist();

        System.out.println("\n🎉 Demo completed! Both LinkedList scenarios work perfectly!");
    }
}
